#!/usr/bin/env python3
"""
Intel-Optimized LSTM Stock Prediction Service for Fine-tuning
Flexible date selection with 3.5-year training windows
Uses 'Adj Close' and 'Volume' features with 2-day prediction horizon
"""

import os
import sys
import json
import logging
from datetime import date, timedelta
from pathlib import Path

# Intel optimization environment variables
os.environ['OMP_NUM_THREADS'] = '16'
os.environ['KMP_AFFINITY'] = 'granularity=fine,compact,1,0'
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '1'
os.environ['TF_ENABLE_BF16_CONVOLUTIONS'] = '1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['PYTHONWARNINGS'] = 'ignore'

# Suppress warnings
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports with Intel optimizations
try:
    import tensorflow as tf
    tf.get_logger().setLevel('ERROR')
    
    # Enable Intel optimizations
    tf.config.threading.set_intra_op_parallelism_threads(16)
    tf.config.threading.set_inter_op_parallelism_threads(16)
    
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    from tensorflow.keras.optimizers import AdamW
    from tensorflow.keras.regularizers import l2
    from tensorflow.keras.callbacks import EarlyStopping
    
    print("✅  TensorFlow-Intel 2.18.0 — oneDNN enabled", file=sys.stderr)
    
except ImportError as e:
    print(f"TensorFlow import error: {e}", file=sys.stderr)
    sys.exit(1)

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler


def load_and_prepare_data(ticker, target_date, use_volume=False):
    """Load data and prepare for training with flexible date selection"""
    try:
        # Load data
        ROOT_DIR = Path(__file__).resolve().parents[1]
        csv_path = ROOT_DIR / "data" / "sp500_adj_close_3y.csv"

        if not csv_path.exists():
            print(f"Data file not found: {csv_path}", file=sys.stderr)
            sys.exit(1)

        # Read price data with proper data types
        df = pd.read_csv(csv_path)
        df['Date'] = pd.to_datetime(df['Date']).dt.date

        # Convert numeric columns to float64
        for col in df.columns:
            if col != 'Date':
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Check if ticker exists
        if ticker not in df.columns:
            print(f"Ticker {ticker} not found in data", file=sys.stderr)
            sys.exit(1)

        # Calculate training end date (3.5 years before target date)
        train_end_date = target_date - timedelta(days=1)  # Day before target
        train_start_date = target_date - timedelta(days=int(3.5 * 365))  # 3.5 years before

        # Filter training data
        train_data = df[(df['Date'] >= train_start_date) & (df['Date'] <= train_end_date)].copy()
        
        if len(train_data) < 100:  # Minimum data requirement
            print(f"Insufficient training data for {ticker} at {target_date}", file=sys.stderr)
            sys.exit(1)

        # Load volume data if requested
        volume_data = None
        if use_volume:
            volume_path = ROOT_DIR / "data" / "sp500_volume_3y.csv"
            if volume_path.exists():
                volume_df = pd.read_csv(volume_path)
                volume_df['Date'] = pd.to_datetime(volume_df['Date']).dt.date

                # Convert numeric columns to float64
                for col in volume_df.columns:
                    if col != 'Date':
                        volume_df[col] = pd.to_numeric(volume_df[col], errors='coerce')

                if ticker in volume_df.columns:
                    volume_data = volume_df[['Date', ticker]].copy()
                    volume_data.columns = ['Date', 'Volume']

        # Get all data for predictions (including target date)
        all_data = df[['Date', ticker]].copy()
        all_data.columns = ['Date', 'Adj Close']
        all_data = all_data.dropna().sort_values('Date')

        # Merge volume data if available
        if use_volume and volume_data is not None:
            volume_data = volume_data.dropna().sort_values('Date')
            all_data = pd.merge(all_data, volume_data, on='Date', how='inner')

            # Also prepare training data with volume
            train_volume = volume_data[
                (volume_data['Date'] >= train_start_date) & 
                (volume_data['Date'] <= train_end_date)
            ].copy()
            train_data_with_volume = pd.merge(
                train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}),
                train_volume,
                on='Date',
                how='inner'
            )
            return all_data, train_data_with_volume, train_end_date
        else:
            return all_data, train_data[['Date', ticker]].rename(columns={ticker: 'Adj Close'}), train_end_date

    except Exception as e:
        print(f"Error loading data: {e}", file=sys.stderr)
        sys.exit(1)


def create_sequences(data, sequence_length=60, use_volume=False, prediction_horizon=2):
    """Create sequences for LSTM training with configurable prediction horizon"""
    X, y = [], []

    if use_volume:
        # data should be 2D array with [price, volume] columns
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target based on price (first column): 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1, 0]  # Current day price
            future_price = data[i + prediction_horizon - 1, 0]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)
    else:
        # data is 1D array with price only
        for i in range(sequence_length, len(data) - prediction_horizon + 1):
            X.append(data[i-sequence_length:i])
            # Binary target: 1 if price increased after prediction_horizon days, 0 if decreased
            current_price = data[i-1]  # Current day price
            future_price = data[i + prediction_horizon - 1]  # Price after prediction_horizon days
            y.append(1 if future_price > current_price else 0)

    return np.array(X), np.array(y)


def build_lstm_model(input_shape):
    """Build Intel-optimized LSTM classifier model with L2 regularization"""
    model = Sequential([
        LSTM(64,
             return_sequences=True,
             input_shape=input_shape,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        LSTM(64,
             return_sequences=False,
             kernel_regularizer=l2(1e-4),
             recurrent_regularizer=l2(1e-4)),
        Dropout(0.1),

        Dense(128,
              activation='relu',
              kernel_regularizer=l2(1e-4)),

        Dense(1, activation='sigmoid',
              kernel_regularizer=l2(1e-4))
    ])

    model.compile(
        optimizer=AdamW(learning_rate=0.0008, weight_decay=1e-4),
        loss='binary_crossentropy',
        metrics=['binary_accuracy']
    )

    return model


def predict_for_date(model, scaler, all_data, target_date, use_volume=False, prediction_horizon=2):
    """Make prediction for specific target date or nearest available date with 2-day horizon"""
    # Find the exact date or nearest available date
    date_mask = all_data['Date'] == target_date

    if not date_mask.any():
        # If exact date not found, find the nearest available date
        print(f"Exact date {target_date} not found, finding nearest available date", file=sys.stderr)

        # Find the closest date before the target date
        available_dates = all_data['Date'].values
        dates_before = [d for d in available_dates if d <= target_date]

        if not dates_before:
            print(f"No data available before {target_date}", file=sys.stderr)
            sys.exit(1)

        # Use the most recent date before target date
        actual_date = max(dates_before)
        date_mask = all_data['Date'] == actual_date
        print(f"Using nearest available date: {actual_date}", file=sys.stderr)
    else:
        actual_date = target_date

    date_idx = all_data[date_mask].index[0]

    # Get 60 days before this date for prediction
    if date_idx < 60:
        print(f"Insufficient data before {target_date}", file=sys.stderr)
        sys.exit(1)

    # Check if we have enough future data for actual label calculation
    if date_idx + prediction_horizon - 1 >= len(all_data):
        print(f"Insufficient future data for {prediction_horizon}-day prediction at {target_date}", file=sys.stderr)
        sys.exit(1)

    # Get sequence data
    if use_volume:
        # Get both price and volume data
        sequence_data = all_data.iloc[date_idx-60:date_idx][['Adj Close', 'Volume']].values
        sequence_scaled = scaler.transform(sequence_data)
        sequence_scaled = sequence_scaled.reshape(1, 60, 2)
    else:
        # Get price data only
        sequence_data = all_data.iloc[date_idx-60:date_idx]['Adj Close'].values
        sequence_scaled = scaler.transform(sequence_data.reshape(-1, 1))
        sequence_scaled = sequence_scaled.reshape(1, 60, 1)

    # Make prediction
    pred_prob = model.predict(sequence_scaled, verbose=0)[0][0]
    predicted_label = 1 if pred_prob > 0.5 else 0

    # Get actual value for 2-day horizon
    current_price = all_data.iloc[date_idx-1]['Adj Close']  # Price at prediction date
    future_price = all_data.iloc[date_idx + prediction_horizon - 1]['Adj Close']  # Price 2 days later
    actual_label = 1 if future_price > current_price else 0

    return {
        "date": actual_date.strftime("%Y-%m-%d"),
        "pred_prob_up": round(float(pred_prob), 4),
        "predicted_label": int(predicted_label),
        "actual_label": int(actual_label),
        "prediction_horizon": prediction_horizon
    }


def determine_result_color(prediction):
    """Determine traffic light color based on prediction accuracy"""
    if prediction["predicted_label"] == prediction["actual_label"]:
        return "green"  # Correct prediction
    else:
        return "red"    # Incorrect prediction


def main():
    """Main function for LSTM fine-tuning service"""
    if len(sys.argv) < 3:
        print("Usage: python lstm_finetuning.py <TICKER> <TARGET_DATE>", file=sys.stderr)
        print("Example: python lstm_finetuning.py AAPL 2024-12-15", file=sys.stderr)
        sys.exit(1)

    ticker = sys.argv[1].upper()
    target_date_str = sys.argv[2]
    
    try:
        target_date = date.fromisoformat(target_date_str)
    except ValueError:
        print(f"Invalid date format: {target_date_str}. Use YYYY-MM-DD", file=sys.stderr)
        sys.exit(1)

    # Enable volume usage by default (can be disabled with 'false' argument)
    use_volume = True
    if len(sys.argv) > 3 and sys.argv[3].lower() == 'false':
        use_volume = False

    try:
        # Load and prepare data
        all_data, train_data, train_end_date = load_and_prepare_data(ticker, target_date, use_volume)

        # Prepare training data
        if use_volume:
            # Use both price and volume data
            train_features = train_data[['Adj Close', 'Volume']].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_features)

            # Create sequences with volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=True, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 2)

            # Build model for 2 features (price + volume)
            model = build_lstm_model((60, 2))
        else:
            # Use price data only
            train_prices = train_data['Adj Close'].values
            scaler = MinMaxScaler(feature_range=(0, 1))
            train_scaled = scaler.fit_transform(train_prices.reshape(-1, 1)).flatten()

            # Create sequences without volume and 2-day prediction horizon
            X_train, y_train = create_sequences(train_scaled, use_volume=False, prediction_horizon=2)
            X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], 1)

            # Build model for 1 feature (price only)
            model = build_lstm_model((60, 1))

        # Early stopping callback
        early_stopping = EarlyStopping(
            monitor='val_binary_accuracy',
            patience=3,
            restore_best_weights=True
        )

        # Train with validation split and early stopping
        model.fit(
            X_train, y_train,
            epochs=10,
            batch_size=256,
            validation_split=0.15,
            callbacks=[early_stopping],
            verbose=0
        )

        # Make prediction for target date with 2-day horizon
        prediction = predict_for_date(model, scaler, all_data, target_date, use_volume, prediction_horizon=2)

        # Determine result color
        result_color = determine_result_color(prediction)
        prediction["result_color"] = result_color

        # Create result
        result = {
            "symbol": ticker,
            "train_until": train_end_date.strftime("%Y-%m-%d"),
            "target_date": target_date.strftime("%Y-%m-%d"),
            "predictions": [prediction]
        }

        # Print JSON result to stdout
        print(json.dumps(result))

    except Exception as e:
        print(f"Error in main execution: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
